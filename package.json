{"name": "hxq-math-app", "version": "1.0.0", "description": "一二年级数学学习辅助App - 全栈项目", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "clean": "rimraf frontend/dist backend/dist frontend/node_modules/.cache", "setup": "cd frontend && npm install && cd ../backend && npm install"}, "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["education", "math", "children", "react", "nodejs", "typescript", "fullstack"], "author": "Development Team", "license": "MIT"}